* libraries and technologies to use
** veilid (rust)
  - https://veilid.com/ (main page)
  - https://gitlab.com/veilid/veilid/-/tree/main/veilid-core (core library)
  - P2P communication layer
    - temp data store
    - onion routing
    - DHT
    - application messaging
      - RPC
    - instant messaging and forum support backbone
*** app notifications
  - and then when someone in the group writes to the record, others could get a watch_dht_values() notification for when the record changes
    that would limit the blast radius, and get you the effect you're looking for.
    people would subscribe to your group by you handing them a dht secret key
    and then they could write notifications to the record created for your group
    and subscribe to changes to that record with the lastest announcements
    i think you can totally do what you're talking about today, just with a bit more guardrails on the design than 'every user of my app automatically
    is subscribed to an app-wide notifications firehose'
    anyway, the magic you're looking for is watch_dht_values, check the rustdoc and you can read on it.
** internet computer protocol (rust)
  - smart contract to automatically convert from one coin/token to Autonomi/ETH for buying storage
    - DeFi so should be OK
  - could also be used to write to a single data store
** zk-STARK solver (rust)
  - https://github.com/starkware-libs/stwo
  - prove that computation is correct without rerunning and/or exposing secret inputs
** garble (rust) and polytune (rust)
  - https://github.com/sine-fdn/polytune (MPC implementation)
  - https://garble-lang.org/landing.html (language)
  - https://sine.foundation/library/002-smpc (high level overview)
  - multi-party computation
  - Used to combine data from multiple users without revealing the secret data making up the computation
    - anonymous metrics tracking for pay-the-creator!
    - Could be used for smart contracts or writing to a common directory?
